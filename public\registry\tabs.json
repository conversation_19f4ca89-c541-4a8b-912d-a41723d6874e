{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "tabs", "title": "tabs", "description": "A minimalistic tab component designed with React and Tailwind CSS.", "author": "<PERSON><PERSON><PERSON><PERSON>", "type": "registry:ui", "dependencies": [], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "tabs.tsx", "content": "'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nexport type TabItem = {\n  id: string | number;\n  name: string;\n  content: string;\n};\n\nexport interface TabsProps {\n  items?: TabItem[];\n  className?: string;\n}\n\nexport default function Tabs({ items = [], className = '' }: TabsProps) {\n  const [activeTab, setActiveTab] = useState<string | number>(items[0]?.id ?? 1);\n\n  const tabVariants = {\n    hidden: { opacity: 0, y: 10 },\n    visible: { opacity: 1, y: 0 },\n    exit: { opacity: 0, y: -10 }\n  };\n\n  const indicatorVariants = {\n    hidden: { scaleX: 0 },\n    visible: { scaleX: 1 }\n  };\n\n  const contentVariants = {\n    hidden: { opacity: 0 },\n    visible: { opacity: 1 },\n    exit: { opacity: 0 }\n  };\n\n  return (\n    <motion.div \n      initial=\"hidden\"\n      animate=\"visible\"\n      variants={tabVariants}\n      className={`bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-100/50 dark:border-gray-800/50 overflow-hidden w-full max-w-2xl ${className}`}\n    >\n      {/* Tabs Header */}\n      <div className='flex border-b border-gray-100/60 dark:border-gray-800/60 bg-gray-50/50 dark:bg-gray-950/50 backdrop-blur-sm'>\n        {items.map((tab) => (\n          <motion.button\n            key={tab.id}\n            onClick={() => setActiveTab(tab.id)}\n            className={`relative px-6 py-4 text-sm font-medium transition-all duration-300 ease-out ${\n              activeTab === tab.id\n                ? 'text-slate-800 dark:text-slate-200'\n                : 'text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300'\n            }`}\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n            aria-selected={activeTab === tab.id}\n            role='tab'\n          >\n            <span className='relative z-10'>{tab.name}</span>\n            {activeTab === tab.id && (\n              <motion.div\n                layoutId=\"activeTab\"\n                className='absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-slate-600 to-slate-800 dark:from-slate-400 dark:to-slate-600'\n                initial=\"hidden\"\n                animate=\"visible\"\n                variants={indicatorVariants}\n                transition={{ duration: 0.3, ease: \"easeOut\" }}\n              />\n            )}\n          </motion.button>\n        ))}\n      </div>\n\n      {/* Content Area */}\n      <div className='relative overflow-hidden'>\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={activeTab}\n            role='tabpanel'\n            className='p-8 bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm text-slate-700 dark:text-slate-300'\n            variants={contentVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n            exit=\"exit\"\n            transition={{ duration: 0.2, ease: \"easeInOut\" }}\n          >\n            {items.find((tab) => tab.id === activeTab)?.content || items[0]?.content}\n          </motion.div>\n        </AnimatePresence>\n      </div>\n    </motion.div>\n  );\n}\n", "type": "registry:ui"}]}