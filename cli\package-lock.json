{"name": "berl<PERSON>", "version": "0.1.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "berl<PERSON>", "version": "0.1.0", "license": "MIT", "dependencies": {"commander": "^13.1.0"}, "bin": {"berlix": "dist/index.js"}}, "node_modules/commander": {"version": "13.1.0", "resolved": "https://registry.npmjs.org/commander/-/commander-13.1.0.tgz", "integrity": "sha512-/rFeCpNJQbhSZjGVwO9RFV3xPqbnERS8MmIQzCtD/zl6gpJuV/bMLuN92oG3F7d8oDEHHRrujSXNUr8fpjntKw==", "engines": {"node": ">=18"}}}}