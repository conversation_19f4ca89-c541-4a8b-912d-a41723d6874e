"use client";

import React, { useState } from "react";
import CodeCopy from "./code-copy";
import { cn } from "@/lib/utils";
import { FaYarn } from "react-icons/fa";
import { SiNpm, SiPnpm } from "react-icons/si";

const PM_COMMANDS = {
  npm: "npx",
  pnpm: "pnpm dlx",
  yarn: "yarn dlx",
  bun: "bunx",
} as const;

type PackageManager = keyof typeof PM_COMMANDS;

type CliProps = {
  command: string;
};

export const Cli = ({ command }: CliProps) => {
  const [pm, setPm] = useState<PackageManager>("npm");

  // Dynamically build the registry URL if command looks like 'add *.json'
  let finalCommand = command;
  if (typeof window !== "undefined") {
    // Match 'add something.json' optionally with quotes
    const match = command.match(/^add\s+"?([\w-]+\.json)"?$/);
    if (match) {
      const file = match[1];
      finalCommand = `add \"${window.location.origin}/registry/${file}\"`;
    }
  }

  const fullCommand = `${PM_COMMANDS[pm]} shadcn@latest ${finalCommand}`;

  return (
    <div className="w-full rounded-lg bg-zinc-100 dark:bg-zinc-950 relative overflow-hidden border border-zinc-200 dark:border-zinc-800">
      {/* PM Selector */}
      <div className="flex items-center justify-between px-4 py-2 bg-zinc-100 dark:bg-zinc-950 border-b border-zinc-200 dark:border-zinc-800">
        <div className="flex items-center gap-2">
          {(Object.keys(PM_COMMANDS) as PackageManager[]).map((name) => {
            const isActive = name === pm;
            return (
              <button
                key={name}
                onClick={() => setPm(name)}
                className={cn(
                  "flex items-center gap-1 px-4 py-2 text-sm font-medium border-b-2 transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-amber-400",
                  "cursor-pointer",
                  isActive
                    ? "border-amber-400 text-zinc-900 dark:text-zinc-50 font-bold bg-white dark:bg-zinc-900/80 shadow-sm rounded-t-md"
                    : "border-transparent text-zinc-500 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-zinc-200 hover:bg-zinc-200/60 dark:hover:bg-zinc-800/60 rounded-t-md"
                )}
                style={{
                  background: isActive ? undefined : "none",
                  borderRadius: "0.5rem 0.5rem 0 0", // top rounded
                  boxShadow: isActive ? "0 2px 8px 0 rgba(0,0,0,0.04)" : undefined,
                  outline: "none",
                  minWidth: 0,
                }}
                aria-selected={isActive}
                role="tab"
                tabIndex={isActive ? 0 : -1}
              >
                {/* Icon for each package manager with color/fade */}
                {name === "npm" && (
                  <SiNpm size={18} color={isActive ? "#CB3837" : "#666"} />
                )}
                {name === "pnpm" && (
                  <SiPnpm size={18} color={isActive ? "#F9DC3E" : "#666"} />
                )}
                {name === "yarn" && (
                  <FaYarn size={18} color={isActive ? "#2C8EBB" : "#666"} />
                )}
                {name === "bun" && (
                  <svg id="Bun" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 80 70" width={18} height={18} style={{ display: 'inline', verticalAlign: 'middle', filter: isActive ? undefined : 'grayscale(1) brightness(0.6)' }}>
                    <title>Bun Logo</title>
                    <path id="Shadow" d="M71.09,20.74c-.16-.17-.33-.34-.5-.5s-.33-.34-.5-.5-.33-.34-.5-.5-.33-.34-.5-.5-.33-.34-.5-.5-.33-.34-.5-.5-.33-.34-.5-.5-.33-.34-.5-.5A26.46,26.46,0,0,1,75.5,35.7c0,16.57-16.82,30.05-37.5,30.05-11.58,0-21.94-4.23-28.83-10.86l.*******.*******.*******.5.5C19.55,65.3,30.14,69.75,42,69.75c20.68,0,37.5-13.48,37.5-30C79.5,32.69,76.46,26,71.09,20.74Z"/>
                    <g id="Body">
                      <path id="Background" d="M73,35.7c0,15.21-15.67,27.54-35,27.54S3,50.91,3,35.7C3,26.27,9,17.94,18.22,13S33.18,3,38,3s8.94,4.13,19.78,10C67,17.94,73,26.27,73,35.7Z" style={{ fill: '#fbf0df' }}/>
                      <path id="Bottom_Shadow" data-name="Bottom Shadow" d="M73,35.7a21.67,21.67,0,0,0-.8-5.78c-2.73,33.3-43.35,34.9-59.32,24.94A40,40,0,0,0,38,63.24C57.3,63.24,73,50.89,73,35.7Z" style={{ fill: '#f6dece' }}/>
                      <path id="Light_Shine" data-name="Light Shine" d="M24.53,11.17C29,8.49,34.94,3.46,40.78,3.45A9.29,9.29,0,0,0,38,3c-2.42,0-5,1.25-8.25,3.13-1.13.66-2.3,1.39-3.54,2.15-2.33,1.44-5,3.07-8,4.7C8.69,18.13,3,26.62,3,35.7c0,.4,0,.8,0,1.19C9.06,15.48,20.07,13.85,24.53,11.17Z" style={{ fill: '#fffefc' }}/>
                      <path id="Top" d="M35.12,5.53A16.41,16.41,0,0,1,29.49,18c-.28.25-.06.73.3.59,3.37-1.31,7.92-5.23,6-13.14C35.71,5,35.12,5.12,35.12,5.53Zm2.27,0A16.24,16.24,0,0,1,39,19c-.12.35.31.65.55.36C41.74,16.56,43.65,11,37.93,5,37.64,4.74,37.19,5.14,37.39,5.49Zm2.76-.17A16.42,16.42,0,0,1,47,17.12a.33.33,0,0,0,.65.11c.92-3.49.4-9.44-7.17-12.53C40.08,4.54,39.82,5.08,40.15,5.32ZM21.69,15.76a16.94,16.94,0,0,0,10.47-9c.18-.36.75-.22.66.18-1.73,8-7.52,9.67-11.12,9.45C21.32,16.4,21.33,15.87,21.69,15.76Z" style={{ fill: '#ccbea7', fillRule: 'evenodd' }}/>
                      <path id="Outline" d="M38,65.75C17.32,65.75.5,52.27.5,35.7c0-10,6.18-19.33,16.53-24.92,3-1.6,5.57-3.21,7.86-4.62,1.26-.78,2.45-1.51,3.6-2.19C32,1.89,35,.5,38,.5s5.62,1.2,8.9,3.14c1,.57,2,1.19,3.07,1.87,2.49,1.54,5.3,3.28,9,5.27C69.32,16.37,75.5,25.69,75.5,35.7,75.5,52.27,58.68,65.75,38,65.75ZM38,3c-2.42,0-5,1.25-8.25,3.13-1.13.66-2.3,1.39-3.54,2.15-2.33,1.44-5,3.07-8,4.7C8.69,18.13,3,26.62,3,35.7,3,50.89,18.7,63.25,38,63.25S73,50.89,73,35.7C73,26.62,67.31,18.13,57.78,13,54,11,51.05,9.12,48.66,7.64c-1.09-.67-2.09-1.29-3-1.84C42.63,4,40.42,3,38,3Z"/>
                    </g>
                    <g id="Mouth">
                      <g id="Background-2" data-name="Background"><path d="M45.05,43a8.93,8.93,0,0,1-2.92,4.71,6.81,6.81,0,0,1-4,1.88A6.84,6.84,0,0,1,34,47.71,8.93,8.93,0,0,1,31.12,43a.72.72,0,0,1,.8-.81H44.26A.72.72,0,0,1,45.05,43Z" style={{ fill: '#b71422' }}/></g>
                      <g id="Tongue"><path id="Background-3" data-name="Background" d="M34,47.79a6.91,6.91,0,0,0,4.12,1.9,6.91,6.91,0,0,0,4.11-1.9,10.63,10.63,0,0,0,1-1.07,6.83,6.83,0,0,0-4.9-2.31,6.15,6.15,0,0,0-5,2.78C33.56,47.4,33.76,47.6,34,47.79Z" style={{ fill: '#ff6164' }}/><path id="Outline-2" data-name="Outline" d="M34.16,47a5.36,5.36,0,0,1,4.19-2.08,6,6,0,0,1,4,1.69c.23-.25.45-.51.66-.77a7,7,0,0,0-4.71-1.93,6.36,6.36,0,0,0-4.89,2.36A9.53,9.53,0,0,0,34.16,47Z"/></g>
                      <path id="Outline-3" data-name="Outline" d="M38.09,50.19a7.42,7.42,0,0,1-4.45-2,9.52,9.52,0,0,1-3.11-5.05,1.2,1.2,0,0,1,.26-1,1.41,1.41,0,0,1,1.13-.51H44.26a1.44,1.44,0,0,1,1.13.51,1.19,1.19,0,0,1,.25,1h0a9.52,9.52,0,0,1-3.11,5.05A7.42,7.42,0,0,1,38.09,50.19Zm-6.17-7.4c-.16,0-.2.07-.21.09a8.29,8.29,0,0,0,2.73,4.37A6.23,6.23,0,0,0,38.09,49a6.28,6.28,0,0,0,3.65-1.73,8.3,8.3,0,0,0,2.72-**********,0,0,0-.2-.09Z"/>
                    </g>
                    <g id="Face">
                      <ellipse id="Right_Blush" data-name="Right Blush" cx="53.22" cy="40.18" rx="5.85" ry="3.44" style={{ fill: '#febbd0' }}/>
                      <ellipse id="Left_Bluch" data-name="Left Bluch" cx="22.95" cy="40.18" rx="5.85" ry="3.44" style={{ fill: '#febbd0' }}/>
                      <path id="Eyes" d="M25.7,38.8a5.51,5.51,0,1,0-5.5-5.51A5.51,5.51,0,0,0,25.7,38.8Zm24.77,0A5.51,5.51,0,1,0,45,33.29,5.5,5.5,0,0,0,50.47,38.8Z" style={{ fillRule: 'evenodd' }}/>
                      <path id="Iris" d="M24,33.64a2.07,2.07,0,1,0-2.06-2.07A2.07,2.07,0,0,0,24,33.64Zm24.77,0a2.07,2.07,0,1,0-2.06-2.07A2.07,2.07,0,0,0,48.75,33.64Z" style={{ fill: '#fff', fillRule: 'evenodd' }}/>
                    </g>
                  </svg>
                )}
                {name}
              </button>
            );
          })}
        </div>
        <CodeCopy
          className="relative top-0 right-0 text-zinc-900 dark:text-zinc-50"
          code={fullCommand}
        />
      </div>

      <div className="text-sm text-left font-mono text-nowrap font-medium bg-white dark:bg-zinc-950 px-4 py-4 overflow-x-auto max-w-full min-w-0">
        <span className="text-amber-400">{PM_COMMANDS[pm]}</span>{" "}
        <span className="text-teal-500">shadcn@latest</span>{" "}
        <span className="text-zinc-700 dark:text-zinc-300">{finalCommand}</span>
      </div>
    </div>
  );
};
