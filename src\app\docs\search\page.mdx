# Search System

The search system provides a macOS-style search interface that automatically indexes all components and documentation pages.

## Features

- **🔍 Instant Search**: Fast, fuzzy search across all components and docs
- **⌨️ Keyboard Shortcuts**: Press `Cmd+K` (Mac) or `Ctrl+K` (Windows/Linux) to open search
- **🎯 Smart Ranking**: Results are ranked by relevance with exact matches first
- **📱 Responsive**: Works perfectly on mobile and desktop
- **🌙 Dark Mode**: Fully supports light and dark themes

## How to Use

### Opening Search

You can open the search modal in three ways:

1. **Click the search icon** in the header
2. **Keyboard shortcut**: `Cmd+K` on Mac or `Ctrl+K` on Windows/Linux
3. **Navigation**: Use arrow keys to navigate, Enter to select, Escape to close

### Search Tips

- Search by component name: "tabs", "loading", "table"
- Search by category: "components", "get started"
- Search by description: "animated", "minimalistic", "youtube-style"

## Auto-Registration

The search system automatically registers:

- All components from the components registry (`src/scripts/components.ts`)
- All navigation items from (`src/constants/navigation.ts`)
- Future components will be automatically indexed when added

## Adding New Components

To add a new component to the search system:

1. **Add to components registry** in `src/scripts/components.ts`:

```typescript
{
  name: "your-component",
  title: "Your Component",
  description: "A brief description of what your component does.",
  path: "../components/ui/your-component.tsx",
  dependencies: [],
}
```

2. **Add to navigation** in `src/constants/navigation.ts`:

```typescript
{
  label: "Components",
  children: [
    // ... existing components
    { label: "Your Component", href: "/docs/your-component" },
  ],
}
```

3. **Create documentation page** at `src/app/docs/your-component/page.mdx`

That's it! Your component will automatically appear in search results.

## Search Algorithm

The search uses a weighted scoring system:

- **Exact title match**: 100 points
- **Title starts with query**: 80 points  
- **Title contains query**: 60 points
- **Description contains query**: 40 points
- **Keywords match**: 30 points
- **Category match**: 20 points

Results are sorted by score (highest first) and limited to 8 items for optimal performance.
