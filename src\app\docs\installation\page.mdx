import { CodeBlock } from "@/components/site/code-block";
import { ComponentRenderer } from "@/components/site/component-renderer";
import { Cli } from "@/components/site/cli";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@/components/site/tabs.tsx";
import { PropsTable } from "@/components/site/props-table.tsx";
import { Table, StatusBadge } from "@/components/ui/table";
import TabsView from "./tabs-view.tsx";
import Install from "./install.tsx";

<Install />

# Vite

Install and configure shadcn/ui for Vite.

# Create project

Start by creating a new React project using `vite`. Select the `React + TypeScript` template:

<Tabs defaultValue="pnpm">
  <TabsList>
    <TabsTrigger value="pnpm">pnpm</TabsTrigger>
    <TabsTrigger value="npm">npm</TabsTrigger>
    <TabsTrigger value="yarn">yarn</TabsTrigger>
    <TabsTrigger value="bun">bun</TabsTrigger>
  </TabsList>
  <TabsContent value="pnpm">
    ```bash
    pnpm create vite@latest
    ```
  </TabsContent>
  <TabsContent value="npm">
    ```bash
    npm create vite@latest
    ```
  </TabsContent>
  <TabsContent value="yarn">
     ```bash
    yarn create vite@latest
    ```
  </TabsContent>
  <TabsContent value="bun">
     ```bash
    bun create vite@latest
    ```
  </TabsContent>
</Tabs>

## Add Tailwind CSS

<Tabs defaultValue="pnpm">
  <TabsList>
    <TabsTrigger value="pnpm">pnpm</TabsTrigger>
    <TabsTrigger value="npm">npm</TabsTrigger>
    <TabsTrigger value="yarn">yarn</TabsTrigger>
    <TabsTrigger value="bun">bun</TabsTrigger>
  </TabsList>
  <TabsContent value="pnpm">
    ```bash
    pnpm add tailwindcss @tailwindcss/vite
    ```
  </TabsContent>
  <TabsContent value="npm">
    ```bash
    npm install tailwindcss @tailwindcss/vite
    ```
  </TabsContent>
  <TabsContent value="yarn">
    ```bash
    yarn add tailwindcss @tailwindcss/vite
    ```
  </TabsContent>
  <TabsContent value="bun">
    ```bash
    bun add tailwindcss @tailwindcss/vite
    ```
  </TabsContent>
</Tabs>

Replace everything in `src/index.css` with the following:

```css
@import "tailwindcss";
```

# Edit tsconfig.json file

The current version of Vite splits TypeScript configuration into three files, two of which need to be edited. Add the `baseUrl` and `paths` properties to the `compilerOptions` section of the `tsconfig.json` and `tsconfig.app.json` files:

```json
{
  "files": [],
  "references": [
    {
      "path": "./tsconfig.app.json"
    },
    {
      "path": "./tsconfig.node.json"
    }
  ],
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

# Edit tsconfig.app.json file

Add the following code to the `tsconfig.app.json` file to resolve paths, for your IDE:

```json
{
  "compilerOptions": {
    // ...
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
      ]
    }
    // ...
  }
}
```

# Update vite.config.ts

Add the following code to the `vite.config.ts` so your app can resolve paths without error:

<Tabs defaultValue="pnpm">
  <TabsList>
    <TabsTrigger value="pnpm">pnpm</TabsTrigger>
    <TabsTrigger value="npm">npm</TabsTrigger>
    <TabsTrigger value="yarn">yarn</TabsTrigger>
    <TabsTrigger value="bun">bun</TabsTrigger>
  </TabsList>
  <TabsContent value="pnpm">
    ```bash
    pnpm add -D @types/node
    ```
  </TabsContent>
  <TabsContent value="npm">
    ```bash
    npm install -D @types/node
    ```
  </TabsContent>
  <TabsContent value="yarn">
    ```bash
    yarn add -D @types/node
    ```
  </TabsContent>
  <TabsContent value="bun">
    ```bash
    bun add -D @types/node
    ```
  </TabsContent>
</Tabs>

Now, update your `vite.config.ts` file:

```typescript
import path from "path"
import tailwindcss from "@tailwindcss/vite"
import react from "@vitejs/plugin-react"
import { defineConfig } from "vite"

// [https://vite.dev/config/](https://vite.dev/config/)
export default defineConfig({
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
})
```

# Run the CLI

Run the `shadcn` init command to setup your project:

<Tabs defaultValue="pnpm">
  <TabsList>
    <TabsTrigger value="pnpm">pnpm</TabsTrigger>
    <TabsTrigger value="npm">npm</TabsTrigger>
    <TabsTrigger value="yarn">yarn</TabsTrigger>
    <TabsTrigger value="bun">bun</TabsTrigger>
  </TabsList>
  <TabsContent value="pnpm">
    ```bash
    pnpm dlx shadcn@latest init
    ```
  </TabsContent>
  <TabsContent value="npm">
    ```bash
    npx shadcn@latest init
    ```
  </TabsContent>
  <TabsContent value="yarn">
    ```bash
    yarn shadcn@latest init
    ```
  </TabsContent>
  <TabsContent value="bun">
    ```bash
    bunx --bun shadcn@latest init
    ```
  </TabsContent>
</Tabs>

You will be asked a few questions to configure `components.json`.

```bash
Which color would you like to use as base color? › Neutral
```

# Add Components

You can now start adding components to your project.

<Tabs defaultValue="pnpm">
  <TabsList>
    <TabsTrigger value="pnpm">pnpm</TabsTrigger>
    <TabsTrigger value="npm">npm</TabsTrigger>
    <TabsTrigger value="yarn">yarn</TabsTrigger>
    <TabsTrigger value="bun">bun</TabsTrigger>
  </TabsList>
  <TabsContent value="pnpm">
    ```bash
    pnpm dlx shadcn@latest add button
    ```
  </TabsContent>
  <TabsContent value="npm">
    ```bash
    npx shadcn@latest add button
    ```
  </TabsContent>
  <TabsContent value="yarn">
    ```bash
    yarn shadcn@latest add button
    ```
  </TabsContent>
  <TabsContent value="bun">
    ```bash
    bunx --bun shadcn@latest add button
    ```
  </TabsContent>
</Tabs>

The command above will add the `Button` component to your project. You can then import it like this:

```tsx
import { Button } from "@/components/ui/button"

function App() {
  return (
    <div className="flex min-h-svh flex-col items-center justify-center">
      <Button>Click me</Button>
    </div>
  )
}

export default App
