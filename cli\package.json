{"name": "berl<PERSON>", "version": "0.1.0", "description": "A CLI tool to install berlix UI components", "bin": {"berlix": "./dist/index.js"}, "scripts": {"build": "tsc", "start": "node dist/index.js"}, "repository": {"type": "git", "url": "https://github.com/reche13/berlix.git", "directory": "cli"}, "keywords": ["ui", "components", "react", "cli"], "author": {"name": "Reche", "url": "https://github.com/reche13"}, "license": "MIT", "dependencies": {"commander": "^13.1.0"}}