# Table of Contents Demo

This page demonstrates the automatic table of contents functionality. The TOC on the right side will show all the `#` headings from this page.

## Introduction

This is a regular h2 heading that won't appear in the TOC since we only track h1 headings.

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

# Getting Started

This is the first main section that will appear in the table of contents. You can click on it in the TOC to jump directly to this section.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

## Subsection

This h2 won't appear in TOC.

Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.

# Features

Here's another main section. Notice how the TOC automatically updates as you scroll through the page.

- **Automatic Detection**: All h1 headings are automatically detected
- **Smooth Scrolling**: Click any TOC item for smooth scrolling
- **Active Highlighting**: Current section is highlighted in the TOC
- **Reading Progress**: Visual progress indicator shows how far you've read

Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.

# Installation

This section shows how the TOC works with longer content. The active section highlighting will update as you scroll.

```bash
npm install your-package
```

At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident.

# Configuration

Another section to demonstrate the scrolling behavior. The TOC will highlight this section when it's in view.

```typescript
const config = {
  autoTOC: true,
  headingLevel: 1, // Only h1 headings
  smoothScroll: true
};
```

Similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga. Et harum quidem rerum facilis est et expedita distinctio.

# Advanced Usage

This section contains more detailed information about advanced features and customization options.

Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus.

## Code Examples

This h2 section won't appear in the TOC, but the content is still important for the overall page structure.

```jsx
import { TableOfContents } from '@/components/site/table-of-contents';

export default function Layout({ children }) {
  return (
    <div>
      <main>{children}</main>
      <TableOfContents />
    </div>
  );
}
```

# Troubleshooting

The final main section of our demo page. This helps test the "Go to bottom" functionality in the TOC.

Temporibus autem quibusdam et aut officiis debitis aut rerum necessitatibus saepe eveniet ut et voluptates repudiandae sint et molestiae non recusandae.

## Common Issues

Another h2 that won't appear in TOC.

Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.

# Conclusion

This is the last main section. The reading progress indicator should show 100% when you reach here.

Thank you for testing the table of contents functionality! The TOC should have automatically detected all the h1 headings on this page and provided smooth navigation between them.
